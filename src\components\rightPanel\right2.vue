<!-- 桥隧健康度 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>桥隧健康度</span>
        <span class="header-subtitle">BRIDGE AND TUNNEL HEALTH</span>
      </div>
    </template>

    <template #content>
      <CEcharts ref="chartRef" :option="option" />
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})
const chartRef = ref()
const currentDataIndex = ref(0)
const timer = ref(null)

// 颜色配置
const color = [
    '#00ffaf',  // I基本完好 - 绿色
    '#306fff',  // II轻微异常 - 蓝色
    '#1E90FF',  // III中等异常 - 浅蓝色
    '#00BFFF',  // IV严重异常 - 天蓝色
]

// 图例数据
const legend = [
    'I基本完好',
    'II轻微异常',
    'III中等异常',
    'IV严重异常'
]

// 多组假数据，用于切换
const mockDataSets = [
    [
        { "name": "I基本完好", "value": 31 },
        { "name": "II轻微异常", "value": 28 },
        { "name": "III中等异常", "value": 18 },
        { "name": "IV严重异常", "value": 15 }
    ],
    [
        { "name": "I基本完好", "value": 25 },
        { "name": "II轻微异常", "value": 35 },
        { "name": "III中等异常", "value": 22 },
        { "name": "IV严重异常", "value": 18 }
    ],
    [
        { "name": "I基本完好", "value": 40 },
        { "name": "II轻微异常", "value": 20 },
        { "name": "III中等异常", "value": 25 },
        { "name": "IV严重异常", "value": 15 }
    ],
    [
        { "name": "I基本完好", "value": 30 },
        { "name": "II轻微异常", "value": 30 },
        { "name": "III中等异常", "value": 20 },
        { "name": "IV严重异常", "value": 20 }
    ]
]

// 当前数据
const seriesData = ref(mockDataSets[0])
const currentHighlightIndex = ref(0)

// 获取当前高亮项的百分比和名称
const getCurrentHighlightInfo = () => {
  const total = seriesData.value.reduce((sum, item) => sum + item.value, 0)
  const currentItem = seriesData.value[currentHighlightIndex.value]
  const percentage = ((currentItem.value / total) * 100).toFixed(1)
  return {
    percentage,
    name: currentItem.name
  }
}

// 自动切换数据
const autoSwitchData = () => {
  timer.value = setInterval(() => {
    currentDataIndex.value = (currentDataIndex.value + 1) % mockDataSets.length
    seriesData.value = mockDataSets[currentDataIndex.value]

    // 更新图表配置
    option.value = createChart()

    // 等待图表更新后重新设置高亮并切换高亮项
    nextTick(() => {
      setTimeout(() => {
        if (chartRef.value && chartRef.value.chart) {
          // 同时切换高亮项
          autoHighlight()
        }
      }, 100)
    })
  }, 2000) // 2秒切换一次
}

// 自动切换高亮项（在数据切换时同步切换）
const autoHighlight = () => {
  if (chartRef.value && chartRef.value.chart) {
    // 取消当前高亮
    chartRef.value.chart.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentHighlightIndex.value,
    })

    // 切换到下一项
    currentHighlightIndex.value = (currentHighlightIndex.value + 1) % seriesData.value.length

    // 高亮新项
    chartRef.value.chart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentHighlightIndex.value,
    })

    // 更新中心文字
    const info = getCurrentHighlightInfo()
    chartRef.value.chart.setOption({
      graphic: [{
        type: 'text',
        left: '19%',
        top: 'center',
        style: {
          text: `${info.percentage}%\n${info.name}`,
          textAlign: 'center',
          fill: '#fff',
          fontSize: 16,
          fontWeight: 'bold'
        }
      }]
    })
  }
}

// 创建图表配置
const createChart = () => {
  const info = getCurrentHighlightInfo()

  return {
    backgroundColor: 'transparent',
    color: color,
    grid: {
        top: '15%',
        left: 0,
        right: '1%',
        bottom: 5,
        containLabel: true,
    },
    legend: {
        orient: 'vertical',
        top: 'center',
        right: '5%',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 35,
        textStyle: {
            color: '#fff',
            fontSize: 13,
            rich: {
              b: {
                width: 100,
                padding: 50
              }
            }
        },
        data: legend,
        formatter: (name) => {
            const item = seriesData.value.find((item) => item.name === name)
            if (!item) return ''
            return [
              `${name}`,
              `{b|${item.value}}`
            ].join('')
        },
    },
    graphic: [
      {
        type: 'text',
        left: '19%',
        top: 'center',
        style: {
          text: `${info.percentage}%\n${info.name}`,
          textAlign: 'center',
          fill: '#fff',
          fontSize: 16,
          fontWeight: 'bold'
        }
      }
    ],
    series: [{
        name: '桥隧健康度',
        type: 'pie',
        center: ['25%', '50%'],
        radius: ['45%', '60%'],
        padAngle: 5,
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        data: seriesData.value,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200
        }
    }],
  }
}

onMounted(() => {
  option.value = createChart()

  // 等待图表渲染完成后设置默认选中和启动自动切换
  nextTick(() => {
    setTimeout(() => {
      if (chartRef.value && chartRef.value.chart) {
        // 设置初始高亮
        chartRef.value.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: currentHighlightIndex.value,
        })

        // 启动自动切换数据
        autoSwitchData()

        // 启动自动切换高亮
        autoHighlight()
      }
    }, 100)
  })
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}
</style>
